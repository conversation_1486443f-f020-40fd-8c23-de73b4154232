﻿LOG 2025/06/18
Save Path: C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Libs\Logs
Task Start: 2025/06/18 04:36:40
Task CommandLine: "C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Libs\n_m3u8dl_re" https://pop1e4.cdn.intigral-ott.net:443/bpk-token/1ah@yshhvtko42uok1ib45j42pokhfw2zj3uyqervvaa/Titles/S0010563001002/S0010563001002.ism/manifest.mpd -mt --append-url-params -sa lang=tr:for=best1 -sv res=540:for=best --tmp-dir "C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Downloads\Meta Aşk.S01-E02.540p.JAWWYTV.WEB-DL.Matego" --save-dir "C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Downloads\Meta Aşk.S01-E02.540p.JAWWYTV.WEB-DL.Matego" --save-name "Meta Aşk.S01-E02.540p.JAWWYTV.WEB-DL.Matego" --key 0cb93255a33dae85aee1981190c0e75c:99a8b13ee1245ea6f70f0a1c2e7304b7 --thread-count 20 --log-level=INFO --check-segments-count=False "--decryption-binary-path=C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Libs\mp4decrypt.exe" --skip-merge --del-after-done=False --mux-import "path=C\:\Users\musta\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Downloads\Meta Aşk.S01-E02.540p.JAWWYTV.WEB-DL.Matego\Meta Aşk.S01-E02.540p.JAWWYTV.WEB-DL.Matego.ar.srt:lang=ara:name=Arabic"

04:36:40.579 INFO : N_m3u8DL-RE (Beta version) 20230628
04:36:40.587 ERROR: MuxAfterDone disabled, MuxImports not allowed!
04:36:40.851 INFO : New version detected! v0.3.0-beta
