import os

device_nvidiashield_lvl1 = {
    'name': 'nvidiashield_lvl1',
    'description': 'nvidashield firmware 7.2.3 lvl1 security level',
    'security_level': 1,
    'session_id_type': 'android',
    'private_key_available': True,
    'vmp': False,
    'send_key_control_nonce': True,
    'keybox_filename': 'keybox',
    'device_cert_filename': 'device_cert',
    'device_private_key_filename': 'device_private_key',
    'device_client_id_blob_filename': 'device_client_id_blob',
    'device_vmp_blob_filename': 'device_vmp_blob'
}

FILES_FOLDER = 'devices'

class DeviceConfig:
    def __init__(self, device):
        self.device_name = device['name']
        self.description = device['description']
        self.security_level = device['security_level']
        self.session_id_type = device['session_id_type']
        self.private_key_available = device['private_key_available']
        self.vmp = device['vmp']
        self.send_key_control_nonce = device['send_key_control_nonce']

        self.keybox_filename = os.path.join(os.path.dirname(__file__), FILES_FOLDER, device['name'], device['keybox_filename'])
        self.device_cert_filename = os.path.join(os.path.dirname(__file__), FILES_FOLDER, device['name'], device['device_cert_filename'])
        self.device_private_key_filename = os.path.join(os.path.dirname(__file__), FILES_FOLDER, device['name'], device['device_private_key_filename'])
        self.device_client_id_blob_filename = os.path.join(os.path.dirname(__file__), FILES_FOLDER, device['name'], device['device_client_id_blob_filename'])
        self.device_vmp_blob_filename = os.path.join(os.path.dirname(__file__), FILES_FOLDER, device['name'], device['device_vmp_blob_filename'])

    def __repr__(self):
        return "DeviceConfig(name={}, description={}, security_level={}, session_id_type={}, private_key_available={}, vmp={})".format(self.device_name, self.description, self.security_level, self.session_id_type, self.private_key_available, self.vmp)

deviceconfig = DeviceConfig(device_nvidiashield_lvl1)

