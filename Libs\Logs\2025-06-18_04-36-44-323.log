﻿LOG 2025/06/18
Save Path: C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Libs\Logs
Task Start: 2025/06/18 04:36:44
Task CommandLine: "C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Libs\n_m3u8dl_re" https://pop1e4.cdn.intigral-ott.net:443/bpk-token/1ah@yshhvtko42uok1ib45j42pokhfw2zj3uyqervvaa/Titles/S0010563001002/S0010563001002.ism/manifest.mpd --append-url-params -sa lang=tr:for=best1 --tmp-dir "C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Downloads\Meta Aşk.S01-E02.540p.JAWWYTV.WEB-DL.Matego" --save-dir "C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Downloads\Meta Aşk.S01-E02.540p.JAWWYTV.WEB-DL.Matego" --save-name "Meta Aşk.S01-E02.540p.JAWWYTV.WEB-DL.Matego_audio" --key 0cb93255a33dae85aee1981190c0e75c:99a8b13ee1245ea6f70f0a1c2e7304b7 --thread-count 20 --log-level=INFO --check-segments-count=False "--decryption-binary-path=C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Libs\mp4decrypt.exe" --skip-merge --del-after-done=False

04:36:44.325 INFO : N_m3u8DL-RE (Beta version) 20230628
04:36:44.333 EXTRA: ffmpeg => C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Libs\ffmpeg.exe
04:36:44.333 INFO : Loading URL: https://pop1e4.cdn.intigral-ott.net:443/bpk-token/1ah@yshhvtko42uok1ib45j42pokhfw2zj3uyqervvaa/Titles/S0010563001002/S0010563001002.ism/manifest.mpd
04:36:44.671 INFO : New version detected! v0.3.0-beta
04:36:44.708 INFO : Content Matched: Dynamic Adaptive Streaming over HTTP
04:36:44.708 INFO : Parsing streams...
04:36:44.722 WARN : Writing meta json
04:36:44.723 INFO : Extracted, there are 7 streams, with 5 basic streams, 1 audio streams, 1 subtitle streams
04:36:44.724 INFO : Vid *CENC 1280x720 | 861 Kbps | video=861805 | avc1.64001F | 427 Segments | ~14m13s
04:36:44.724 INFO : Vid *CENC 960x540 | 516 Kbps | video=516675 | avc1.64001F | 427 Segments | ~14m13s
04:36:44.725 INFO : Vid *CENC 768x432 | 369 Kbps | video=369019 | avc1.64001E | 427 Segments | ~14m13s
04:36:44.725 INFO : Vid *CENC 512x288 | 201 Kbps | video=201250 | avc1.640015 | 427 Segments | ~14m13s
04:36:44.726 INFO : Vid *CENC 384x216 | 137 Kbps | video=137664 | avc1.640014 | 427 Segments | ~14m13s
04:36:44.726 INFO : Aud *CENC audio_tur=95997 | 95 Kbps | mp4a.40.2 | tr | 2CH | 427 Segments | ~14m13s
04:36:44.726 INFO : Sub textstream_ara=1000 | ar | stpp.ttml.im1t | 409 Segments | ~13m37s
04:36:44.726 EXTRA: AudioFilter => LanguageReg: tr For: best1
04:36:44.727 INFO : Parsing streams...
04:36:44.728 INFO : Selected streams:
04:36:44.729 INFO : Aud *CENC audio_tur=95997 | 95 Kbps | mp4a.40.2 | tr | 2CH | 427 Segments | ~14m13s
04:36:44.729 WARN : Writing meta json
04:36:44.730 INFO : Save Name: Meta Aşk.S01-E02.540p.JAWWYTV.WEB-DL.Matego_audio
04:36:44.730 INFO : Start downloading...Aud audio_tur=95997 | 95 Kbps | mp4a.40.2 | tr | 2CH
04:36:44.730 WARN : When CENC encryption is detected, binary merging is automatically enabled
04:36:44.731 WARN : Type: cenc
04:36:44.731 WARN : PSSH(WV): CAESEAy5MlWjPa6FruGYEZDA51waGnZlcmltYXRyaXhndWxmZGlnaXRhbG1lZGlhIhdyPVMwMDEwNTYzMDAxMDAyJnM9MTAyNyoFU0RfSEQ=
04:36:44.732 WARN : KID: 0cb93255a33dae85aee1981190c0e75c
04:36:44.732 WARN : Reading media info...
04:36:44.768 INFO : NaN: Audio, aac (mp4a), 95 kb/s
04:36:44.771 INFO : Done
