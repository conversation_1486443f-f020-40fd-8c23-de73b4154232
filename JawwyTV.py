global proxies
import os
import re
import requests
import base64
import subprocess
import json
from bs4 import BeautifulSoup
from langcodes import Language
from urllib.parse import unquote
from prettytable import PrettyTable
from pywidevine.cdm import deviceconfig
from pywidevine.decrypt.wvdecryptcustom import WvDecrypt

headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'}
proxies = {}
LibsPath = os.path.join(os.getcwd(), 'Libs')

def GetProxies():
    global proxies
    Proxy = open(os.path.join(LibsPath, 'JawwyTV.txt'), 'r').read().strip()
    proxies = {'http': Proxy, 'https': Proxy}

def Login():
    global deviceId
    with open(os.path.join(LibsPath, 'JawwyTV.json'), 'r') as file:
        Data = json.load(file)
        deviceId = Data['deviceId']

def Clean(Input):
    Text = ''.join((i for i in Input if i not in '\\/:*?"<>|™-'))
    for T in range(len(Text)):
        Text = Text.replace('  ', ' ').replace(' ', '.').replace('..', '.').replace("'", '').replace(',', '').replace('ş', 's').replace('ç', 'c')
    if Text[-1] == '.':
        Text = Text[:-1]
    return Text.strip()

def Download(Folder, Name, VidID):
    output_file = os.path.join(Folder, Name + '.mkv')
    if os.path.exists(output_file):
        print(f"⏭️ File already exists: {output_file}")
        return

    if Images:
        DirImages = os.path.join(Folder, 'Images')
        if not os.path.exists(DirImages):
            os.mkdir(DirImages)
        for I in Images:
            open(os.path.join(DirImages, I.split('>>')[1]), 'wb').write(requests.get(url=I.split('>>')[0]).content)
    Images.clear()

    Subs = ''
    if Subtitles:
        subprocess.call(os.path.join(LibsPath, 'n_m3u8dl_re') + ' ' + VidUrl + ' -mt --append-url-params' + ' -ss lang="' + '|'.join(Subtitles) + '":for=best' + str(len(Subtitles)) + ' --tmp-dir "' + Folder + '" --save-dir "' + Folder + '" --save-name "' + Name + '"' + ' --thread-count 20 --log-level=ERROR --no-log --check-segments-count=False')

    for S in Subtitles:
        subprocess.call([os.path.join(LibsPath, 'SubtitleEdit'), '/convert', os.path.join(Folder, Name + '.' + S + '.srt'), 'SubRip', '/overwrite', '/FixRtlViaUnicodeChars', '/RemoveFormatting'], stdout=subprocess.DEVNULL, stderr=subprocess.STDOUT)
        Subs += ' --mux-import path="' + os.path.join(Folder, Name + '.' + S + '.srt').replace(':', '\\:') + '":lang=' + Language.get(S).to_alpha3() + ':name="' + Language.get(S).display_name().title() + '"'

    # Fixed code - Download video and audio separately first
    print(f"🔑 Extracting keys: {len(Keys.split('--key')) - 1} key(s)")
    print(f"📹 Video quality: {VidID}p")
    print(f"🎵 Audio tracks: {', '.join(Audios)}")

    # Download video and audio separately first
    command = (
        f'"{os.path.join(LibsPath, "n_m3u8dl_re")}" "{VidUrl}" -mt --append-url-params '
        f'-sa lang="{"|".join(Audios)}":for=best{len(Audios)} '
        f'-sv res="{VidID}":for=best '
        f'--tmp-dir "{Folder}" --save-dir "{Folder}" --save-name "{Name}" '
        f'{Keys} '
        f'--thread-count 20 --log-level=ERROR --no-log --check-segments-count=False '
        f'--decryption-binary-path="{os.path.join(LibsPath, "mp4decrypt.exe")}" '
        f'--concurrent-download --live-fix-vtt-by-audio --no-mux {Subs}'
    )

    print("🚀 Starting download and decryption...")

    # Attempt download with retry on failure
    max_retries = 3
    for attempt in range(max_retries):
        if attempt > 0:
            print(f"🔄 Retry attempt {attempt + 1}/{max_retries}...")

        result = subprocess.call(command, shell=True)

        if result == 0:
            print("✅ Download and decryption successful!")

            # Search for separate video and audio files
            video_file = None
            audio_file = None

            # Search folder for downloaded files
            all_files = []
            try:
                all_files = os.listdir(Folder)
            except:
                print("❌ Error reading folder contents")
                result = 1
                continue

            for file in all_files:
                if file.startswith(Name):
                    full_path = os.path.join(Folder, file)
                    if file.endswith('.mp4') or file.endswith('.m4v'):
                        if any(keyword in file.lower() for keyword in ['video', '_v_', 'vid']):
                            video_file = full_path
                            print(f"🎬 Found video file: {file}")
                        elif any(keyword in file.lower() for keyword in ['audio', '_a_', 'aud']):
                            audio_file = full_path
                            print(f"🎵 Found audio file: {file}")
                        elif video_file is None:  # If no specific video file found, take first file
                            video_file = full_path
                            print(f"📹 Potential video file: {file}")

            # If no separate files found, look for merged mkv file
            output_file = os.path.join(Folder, Name + '.mkv')
            if os.path.exists(output_file) and os.path.getsize(output_file) > 1024:
                print(f"📁 File saved at: {output_file}")
                print(f"📊 File size: {os.path.getsize(output_file) / (1024*1024):.2f} MB")

                # Check file integrity using ffprobe
                print("🔍 Checking file integrity...")
                ffprobe_path = os.path.join(LibsPath, "ffprobe.exe")
                if os.path.exists(ffprobe_path):
                    check_cmd = f'"{ffprobe_path}" -v error -select_streams v:0 -show_entries stream=codec_name -of csv=p=0 "{output_file}"'
                    try:
                        probe_result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=30)
                        if probe_result.returncode == 0 and probe_result.stdout.strip():
                            print(f"✅ File is valid - Video codec: {probe_result.stdout.strip()}")
                            break
                        else:
                            print("❌ File is corrupted or unreadable!")
                            result = 1
                    except subprocess.TimeoutExpired:
                        print("⏰ File check timeout")
                        break
                    except Exception as e:
                        print(f"⚠️ Error checking file: {str(e)}")
                        break
                else:
                    print("⚠️ ffprobe.exe not found - skipping file check")
                    break

            # If separate files found, merge them
            elif video_file and audio_file:
                print("🔧 Merging video and audio files...")
                output_file = os.path.join(Folder, Name + '.mkv')

                mkvmerge_path = os.path.join(LibsPath, "mkvmerge.exe")
                if not os.path.exists(mkvmerge_path):
                    print("❌ mkvmerge.exe not found! Trying ffmpeg...")
                    # Use ffmpeg as alternative
                    ffmpeg_path = os.path.join(LibsPath, "ffmpeg.exe")
                    if os.path.exists(ffmpeg_path):
                        mux_cmd = f'"{ffmpeg_path}" -i "{video_file}" -i "{audio_file}" -c copy -map 0:v:0 -map 1:a:0 "{output_file}"'
                    else:
                        print("❌ No muxing tools available!")
                        result = 1
                        continue
                else:
                    mux_cmd = f'"{mkvmerge_path}" -o "{output_file}" "{video_file}" "{audio_file}"'

                    # Add subtitles if found
                    if Subs:
                        for S in Subtitles:
                            sub_file = os.path.join(Folder, Name + '.' + S + '.srt')
                            if os.path.exists(sub_file):
                                mux_cmd += f' --language 0:{Language.get(S).to_alpha3()} --track-name 0:"{Language.get(S).display_name().title()}" "{sub_file}"'

                print(f"🔧 Executing mux command...")
                mux_result = subprocess.call(mux_cmd, shell=True)

                if mux_result == 0 and os.path.exists(output_file) and os.path.getsize(output_file) > 1024:
                    print("✅ Files merged successfully!")
                    print(f"📁 File saved at: {output_file}")
                    print(f"📊 File size: {os.path.getsize(output_file) / (1024*1024):.2f} MB")

                    # Delete temporary files
                    try:
                        if os.path.exists(video_file):
                            os.remove(video_file)
                        if os.path.exists(audio_file):
                            os.remove(audio_file)
                        print("🗑️ Temporary files deleted")
                    except Exception as e:
                        print(f"⚠️ Cannot delete temporary files: {str(e)}")
                    break
                else:
                    print("❌ Failed to merge files!")
                    if os.path.exists(output_file):
                        print(f"⚠️ Merged file size: {os.path.getsize(output_file)} bytes")
                    result = 1
            else:
                print("⚠️ No valid files found!")
                print("📋 Files found in folder:")
                for file in all_files:
                    if file.startswith(Name):
                        file_path = os.path.join(Folder, file)
                        size_mb = os.path.getsize(file_path) / (1024*1024) if os.path.exists(file_path) else 0
                        print(f"   - {file} ({size_mb:.2f} MB)")

                # Last attempt: look for any video file
                if video_file and not audio_file:
                    print("🎬 Found video file only, copying as is...")
                    output_file = os.path.join(Folder, Name + '.mkv')
                    try:
                        import shutil
                        shutil.copy2(video_file, output_file)
                        print(f"📁 File saved at: {output_file}")
                        print(f"📊 File size: {os.path.getsize(output_file) / (1024*1024):.2f} MB")
                        break
                    except Exception as e:
                        print(f"❌ Failed to copy file: {str(e)}")

                result = 1

        if result != 0:
            if attempt < max_retries - 1:
                print(f"❌ Attempt {attempt + 1} failed, error code: {result}")
                import time
                time.sleep(2)  # Wait before retry
            else:
                print(f"❌ Final failure after {max_retries} attempts! Error code: {result}")
                print("💡 Check:")
                print("   - Key validity")
                print("   - n_m3u8dl_re tool exists")
                print("   - mp4decrypt.exe exists")
                print("   - Internet connection")

# دالة لتنظيف اسم المجلد من الأحرف غير المسموح بها
def clean_folder_name(name):
    return re.sub(r'[<>:"/\\|?*]', '', name).strip()

def Start(Type, ID):
    FolderDownloads = os.path.join(os.getcwd(), 'Downloads')
    if not os.path.exists(FolderDownloads):
        os.mkdir(FolderDownloads)

    # تنظيف اسم المجلد
    clean_title = clean_folder_name(Title)

    if Type != 'Movie' and Type != 'Episode':
        FolderMain = os.path.join(FolderDownloads, clean_title)
        if not os.path.exists(FolderMain):
            os.mkdir(FolderMain)

    if Type == 'Series':
        Is = False
        Count = 0
        for S in SeasonID:
            TitMain = clean_title + '.S' + (f'0{S}' if int(S) < 10 else str(S))
            FolderSub = os.path.join(FolderMain, TitMain)
            if not os.path.exists(FolderSub):
                os.mkdir(FolderSub)
            if SeasonID.index(S) == 0:
                print('[2] Extracting.. 2/3')
            for E in SeasonEpisodes[SeasonID.index(S)]:
                ShowFormats(E)
                if not Is:
                    print('[3] Downloading.. 3/3')
                    Is = True
                VidID = Videos[int(VideoID) - 1] if int(VideoID) <= len(Resolutions) else Videos[-1]
                Tit = f'{TitMain}-E{SeasonEpisodes[SeasonID.index(S)].index(E) + 1:02d}.{VidID}p.JAWWYTV.WEB-DL.Matego'
                Download(FolderSub, Tit, VidID)
            Count += 1

    elif Type in ['Season', 'Range', 'List', 'Movie', 'Episode']:
        if Type == 'Season':
            TitMain = f"{clean_title}.S{int(ID):02d}"
            FolderSub = os.path.join(FolderMain, TitMain)
            if not os.path.exists(FolderSub):
                os.mkdir(FolderSub)
            for E in SeasonEpisodes[SeasonID.index(int(ID))]:
                print('[2] Extracting.. 2/3')
                ShowFormats(str(E))
                print('[3] Downloading.. 3/3')
                VidID = Videos[int(VideoID) - 1] if int(VideoID) <= len(Resolutions) else Videos[-1]
                Tit = f"{TitMain}-E{SeasonEpisodes[SeasonID.index(int(ID))].index(E) + 1:02d}.{VidID}p.JAWWYTV.WEB-DL.Matego"
                Download(FolderSub, Tit, VidID)
        else:
            print('[2] Extracting.. 2/3')
            ShowFormats(ID)
            print('[3] Downloading.. 3/3')
            VidID = Videos[int(VideoID) - 1] if int(VideoID) <= len(Resolutions) else Videos[-1]
            Tit = f"{clean_title}.{VidID}p.JAWWYTV.WEB-DL.Matego"
            FolderMain = os.path.join(FolderDownloads, Tit)
            if not os.path.exists(FolderMain):
                os.mkdir(FolderMain)
            Download(FolderMain, Tit, VidID)

    print('Done :)')


def ChooseVideo():
    global VideoID
    Input = input(">> Choose Video's Resolution No.: ").strip()
    if Input.isnumeric() and int(Input) > 0 and (int(Input) - 1 < len(Videos)):
        VideoID = Input
    else:
        ChooseVideo()

def ChooseRange():
    global Range
    Input = input(">> Do You Want 1080-4K Resuolution 'Y' Or 'N': ").strip().upper()
    if Input == 'Y':
        Range = 'Titles_HEVC'
    elif Input == 'N':
        Range = 'Titles'
    else:
        ChooseRange()

def ShowFormats(ID):
    global VidUrl
    global Videos
    global Resolutions
    global Audios
    global Subtitles
    global Keys
    Videos = []
    Audios = []
    Subtitles = []
    Resolutions = []
    Bandwidths = []
    Auds = []
    Subs = []
    if not Range:
        ChooseRange()
    response = requests.get(url='https://uselector.cdn.intigral-ott.net' + ID.split('.net')[1].replace('Titles', Range), headers=headers, proxies=proxies)
    VidUrl = response.url
    Data = BeautifulSoup(response.text, 'xml')
    for D in Data.find_all('AdaptationSet', {'contentType': 'text'}):
        Subs.append(Language.get(D['lang']).to_alpha3().title())
        Subtitles.append(D['lang'])
    if Subs:
        Subs, Subtitles = [list(tuple) for tuple in zip(*sorted(zip(Subs, Subtitles)))]
    for D in Data.find_all('AdaptationSet', {'contentType': 'audio'}):
        AN = Language.get(D['lang']).to_alpha3().title()
        if AN not in Auds:
            Auds.append(AN)
            Audios.append(D['lang'])
    Auds, Audios = [list(tuple) for tuple in zip(*sorted(zip(Auds, Audios)))]
    for D in Data.find_all('AdaptationSet', {'contentType': 'video'}):
        for R in D.find_all('Representation'):
            Bandwidths.append(int(R['bandwidth']))
            Resolutions.append(int(R['height']))
            Videos.append(R['height'])
    Bandwidths, Resolutions, Videos = [list(tuple) for tuple in zip(*sorted(zip(Bandwidths, Resolutions, Videos)))]
    for D in Data.find_all('ContentProtection', {'value': 'cenc'}):
        aob = bytearray(b'\x00\x00\x002pssh\x00\x00\x00\x00')
        aob.extend(bytes.fromhex('edef8ba979d64acea3c827dcd51d21ed'))
        aob.extend(b'\x00\x00\x00\x12\x12\x10')
        aob.extend(bytes.fromhex(D.get('cenc:default_KID').replace('-', '')))
        Pssh = base64.b64encode(bytes.fromhex(aob.hex())).decode()
        break
    try:
        print("🔐 Getting DRM certificate...")
        response = requests.post(url='https://wv.drm.intigral-ott.net:8063/?deviceId=' + deviceId, data='\x08\x04')
        if response.status_code != 200:
            print(f"❌ Failed to get certificate: {response.status_code}")
            return

        Cert = base64.b64encode(response.content).decode('utf-8')
        wvdecrypt = WvDecrypt(Pssh, Cert, deviceconfig.device_nvidiashield_lvl1)
        chal = wvdecrypt.get_challenge()

        print("🔑 Requesting license...")
        response = requests.post(url='https://wv.drm.intigral-ott.net:8063/?deviceId=' + deviceId, data=chal)
        if response.status_code != 200:
            print(f"❌ Failed to get license: {response.status_code}")
            return

        wvdecrypt.update_license(base64.b64encode(response.content))
        success, keyswvdecrypt = wvdecrypt.start_process()

        if not success:
            print("❌ Failed to extract keys!")
            return

    except Exception as e:
        print(f"❌ DRM process error: {str(e)}")
        return
    Keys = ''
    print(f"🔑 تم استخراج {len(keyswvdecrypt)} مفتاح للتشفير")
    for key in keyswvdecrypt:
        # التأكد من تنسيق المفتاح الصحيح (KID:KEY)
        if ':' in key and len(key.split(':')) == 2:
            Keys = Keys + ' --key ' + key
            print(f"✅ مفتاح صالح: {key[:16]}...{key[-16:]}")
        else:
            print(f"❌ مفتاح غير صالح: {key}")

    if not Keys.strip():
        print("❌ لم يتم العثور على مفاتيح صالحة!")
        return
    Row = ['No.', 'Resolution', 'Bandwidth', 'Audios']
    if Subs:
        Row.append('Subtitles')
    Table = PrettyTable(Row)
    Count = 1
    for B in Bandwidths:
        Row = [Count, Resolutions[Bandwidths.index(B)], B, ','.join(Auds)]
        if Subs:
            Row.append(','.join(Subs))
        Table.add_row(Row)
        Count += 1
    if not VideoID:
        print(Table)
        ChooseVideo()

def MainSeason(ID):
    global Title
    Input = input(">> Enter Episode No. Or Range 'From-To' Or List 'No,No' Or 'A' For All Episodes: ").strip()
    if Input.upper() == 'A':
        Start('Season', str(ID))
    else:
        if '-' in Input and Input.split('-')[0].isnumeric() and (int(Input.split('-')[0]) > 0) and (int(Input.split('-')[0]) <= len(SeasonEpisodes[SeasonID.index(int(ID))])):
            if Input.split('-')[1].isnumeric() and int(Input.split('-')[1]) > 0 and (int(Input.split('-')[1]) <= len(SeasonEpisodes[SeasonID.index(int(ID))])):
                Start('Range', str(ID) + '>>' + str(int(str(Input.split('-')[0]).strip()) - 1) + '>>' + str(Input.split('-')[1]).strip())
        if ',' in Input:
            List = Input.split(',')
            Arr = []
            for L in List:
                if int(L) > 0 and int(L) - 1 <= len(SeasonEpisodes[SeasonID.index(int(ID))]):
                    Arr.append(str(int(L) - 1))
            if Arr:
                Start('List', str(ID) + '>>' + ','.join(Arr))
            else:
                MainSeason(ID)
        elif Input.isnumeric() and int(Input) > 0 and (int(Input) <= len(SeasonEpisodes[SeasonID.index(int(ID))])):
            Title = Title + '.S'
            if int(ID) < 10:
                Title = Title + '0'
            Title = Title + str(ID) + '-E'
            if int(Input) < 10:
                Title = Title + '0'
            Title = Title + str(Input)
            Start('Episode', str(SeasonEpisodes[SeasonID.index(int(ID))][int(Input) - 1]))
        else:
            MainSeason(ID)

def MainSeries():
    Input = input(">> Enter Season No. Or Range 'From-To' Or List 'No,No' Or 'A' For All Seasons: ").strip()
    if Input.upper() == 'A':
        Start('Series', '')
    elif '-' in Input:
        for S in range(int(Input.split('-')[0]), int(Input.split('-')[1]) + 1):
            Start('Season', str(S))
    elif ',' in Input:
        for S in Input.split(','):
            Start('Season', S)
    elif Input.isnumeric() and int(Input) > 0 and (int(Input) in SeasonID):
        MainSeason(Input)
    else:
        MainSeries()

import requests
import base64
from prettytable import PrettyTable
from urllib.parse import unquote, urlparse, parse_qs

# تصحيح دالة Check لتحديثات طريقة الرابط الجديدة
def Check(Input):
    global Title
    global SeasonEpisodes
    global Images
    global VideoID
    global Range
    global SeasonID
    
    VideoID = ''
    Range = ''
    SeasonID = []
    SeasonEpisodes = []
    Images = []

    try:
        # تحليل الرابط لاستخراج قيمة 'movie' أو 'series' و 'guid'
        parsed_url = urlparse(Input)
        query_params = parse_qs(parsed_url.query)

        encoded_id = query_params.get('movie') or query_params.get('series')

        if encoded_id:
            decoded_id = base64.b64decode(encoded_id[0]).decode('utf-8')
            params = {'form': 'cjson', 'byId': decoded_id, 'range': '0-1000'}
        else:
            print("❌ Error: 'movie' or 'series' parameter not found in URL")
            return
    except (IndexError, base64.binascii.Error, KeyError) as e:
        print(f"Error processing ID: {e}")
        return

    # اختيار رابط API بناءً على نوع المحتوى
    if 'movie' in Input.lower():
        Url = 'https://feed.entertainment.tv.theplatform.eu/f/BgsWgC/p-gm-all-vod-movies-pavail'
    elif 'series' in Input.lower():
        Url = 'https://feed.entertainment.tv.theplatform.eu/f/BgsWgC/p-gm-all-series'
    else:
        print("❌ Error: Invalid URL format")
        return

    try:
        response = requests.get(url=Url, params=params)
        response.raise_for_status()
        Data = response.json()
    except requests.exceptions.RequestException as e:
        print(f"❌ HTTP Error: {e}")
        return

    if not Data.get('entries'):
        print('Sorry, Content Not Found')
        return

    Data = Data['entries'][0]
    Title = Data.get('title', 'Unknown Title')

    for I in Data.get('thumbnails', {}):
        Images.append(Data['thumbnails'][I]['url'] + '>>' + I.split('-')[0] + '.jpg')

    if Data.get('programType', '').lower() == 'movie':
        Type = 'Movie'
        Table = PrettyTable(['No.', 'Type', 'Title'])
        Table.add_row(['1', Type, Title.replace('.', ' ')])

        for M in Data.get('media', [{}])[0].get('content', []):
            if M.get('assetTypes', [''])[0] == 'Video':
                Mpd = M.get('downloadUrl', '').split('{switch:dash}')[1].split('{')[0]
                break
        else:
            print("❌ No valid MPD URL found")
            return

        print(Table)
        return Start(Type, Mpd)

    elif Data.get('programType', '').lower() == 'series':
        Type = 'Series'
        Table = PrettyTable(['No.', 'Type', 'Episodes'])

        params = {'form': 'cjson', 'bySeriesId': Data['id'], 'sort': 'tvSeasonNumber', 'range': '0-1000'}
        try:
            response = requests.get(url='https://feed.entertainment.tv.theplatform.eu/f/BgsWgC/p-gm-AllTVSeasonFeed', params=params)
            response.raise_for_status()
            Data2 = response.json().get('entries', [])
        except requests.exceptions.RequestException as e:
            print(f"❌ HTTP Error: {e}")
            return

        for S in Data2:
            params = {'form': 'cjson', 'byTvSeasonId': S['id'], 'sort': 'tvSeasonEpisodeNumber', 'range': '0-1000'}
            try:
                response = requests.get(url='https://feed.entertainment.tv.theplatform.eu/f/BgsWgC/p-gm-all-vod-episodes-pavail', params=params)
                response.raise_for_status()
                Data3 = response.json().get('entries', [])
            except requests.exceptions.RequestException as e:
                print(f"❌ HTTP Error: {e}")
                continue

            Temp = []
            for E in Data3:
                for M in E.get('media', [{}])[0].get('content', []):
                    if M.get('assetTypes', [''])[0] == 'Video':
                        Temp.append(M.get('downloadUrl', '').split('{switch:dash}')[1].split('{')[0])
                        break

            SeasonID.append(S.get('tvSeasonNumber', 'Unknown'))
            SeasonEpisodes.append(Temp)
            Table.add_row([S.get('tvSeasonNumber', 'Unknown'), 'Season', len(Temp)])

        print(Table)
        return MainSeries()


def Config():
    Input = input('>> Enter Link: ').strip()
    if Input:
        print('[1] Retrieving.. 1/3')
        GetProxies()
        Login()
        Check(unquote(Input))
    Config()
print('Omar A Muthana :)\n')
Config()