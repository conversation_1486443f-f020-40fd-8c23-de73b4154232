﻿<?xml version="1.0" encoding="utf-8"?>
<!-- Created with Broadpeak BkS350 Origin Packager (version=1.11.16-26430) -->
<MPD xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="urn:mpeg:dash:schema:mpd:2011" xmlns:cenc="urn:mpeg:cenc:2013" xsi:schemaLocation="urn:mpeg:dash:schema:mpd:2011 http://standards.iso.org/ittf/PubliclyAvailableStandards/MPEG-DASH_schema_files/DASH-MPD.xsd" type="static" mediaPresentationDuration="PT12M0.960S" maxSegmentDuration="PT3S" minBufferTime="PT12S" profiles="urn:mpeg:dash:profile:isoff-live:2011">
  <Period id="1" duration="PT12M0.960S">
    <BaseURL>dash/</BaseURL>
    <AdaptationSet id="1" group="1" contentType="audio" lang="tr" segmentAlignment="true" audioSamplingRate="48000" mimeType="audio/mp4" codecs="mp4a.40.2" startWithSAP="1">
      <AudioChannelConfiguration schemeIdUri="urn:mpeg:dash:23003:3:audio_channel_configuration:2011" value="2"/>
      <!-- Common Encryption -->
      <ContentProtection schemeIdUri="urn:mpeg:dash:mp4protection:2011" value="cenc" cenc:default_KID="7BA1B3C0-E41B-4CB6-C0F3-C3E0A1A87D00">
      </ContentProtection>
      <!-- PlayReady -->
      <ContentProtection schemeIdUri="urn:uuid:9A04F079-9840-4286-AB92-E65BE0885F95" value="MSPR 2.0">
      </ContentProtection>
      <!-- Widevine -->
      <ContentProtection schemeIdUri="urn:uuid:EDEF8BA9-79D6-4ACE-A3C8-27DCD51D21ED">
      </ContentProtection>
      <Role schemeIdUri="urn:mpeg:dash:role:2011" value="main"/>
      <SegmentTemplate timescale="48000" initialization="S0010563001001-$RepresentationID$.dash" media="S0010563001001-$RepresentationID$-$Time$.dash">
        <SegmentTimeline>
          <S t="0" d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="96128"/>
          <S d="49152"/>
        </SegmentTimeline>
      </SegmentTemplate>
      <Representation id="audio_tur=95995" bandwidth="95995">
      </Representation>
    </AdaptationSet>
    <AdaptationSet id="2" group="3" contentType="text" lang="ar" mimeType="application/mp4" codecs="stpp.ttml.im1t" startWithSAP="1">
      <Role schemeIdUri="urn:mpeg:dash:role:2011" value="subtitle"/>
      <SegmentTemplate timescale="1000" initialization="S0010563001001-$RepresentationID$.dash" media="S0010563001001-$RepresentationID$-$Time$.dash">
        <SegmentTimeline>
          <S t="0" d="2000" r="324"/>
          <S d="1840"/>
        </SegmentTimeline>
      </SegmentTemplate>
      <Representation id="textstream_ara=1000" bandwidth="1000">
      </Representation>
    </AdaptationSet>
    <AdaptationSet id="3" group="2" contentType="video" par="16:9" minBandwidth="197851" maxBandwidth="1495476" maxWidth="1920" maxHeight="1080" segmentAlignment="true" sar="1:1" frameRate="25" mimeType="video/mp4" startWithSAP="1">
      <!-- Common Encryption -->
      <ContentProtection schemeIdUri="urn:mpeg:dash:mp4protection:2011" value="cenc" cenc:default_KID="7BA1B3C0-E41B-4CB6-C0F3-C3E0A1A87D00">
      </ContentProtection>
      <!-- PlayReady -->
      <ContentProtection schemeIdUri="urn:uuid:9A04F079-9840-4286-AB92-E65BE0885F95" value="MSPR 2.0">
      </ContentProtection>
      <!-- Widevine -->
      <ContentProtection schemeIdUri="urn:uuid:EDEF8BA9-79D6-4ACE-A3C8-27DCD51D21ED">
      </ContentProtection>
      <Role schemeIdUri="urn:mpeg:dash:role:2011" value="main"/>
      <SegmentTemplate timescale="600" initialization="S0010563001001-$RepresentationID$.dash" media="S0010563001001-$RepresentationID$-$Time$.dash">
        <SegmentTimeline>
          <S t="0" d="1200" r="359"/>
          <S d="576"/>
        </SegmentTimeline>
      </SegmentTemplate>
      <Representation id="video=197851" bandwidth="197851" width="512" height="288" codecs="hvc1.1.6.L63.90" scanType="progressive">
      </Representation>
      <Representation id="video=334453" bandwidth="334453" width="768" height="432" codecs="hvc1.1.6.L90.90" scanType="progressive">
      </Representation>
      <Representation id="video=433026" bandwidth="433026" width="960" height="540" codecs="hvc1.1.6.L90.90" scanType="progressive">
      </Representation>
      <Representation id="video=641481" bandwidth="641481" width="1280" height="720" codecs="hvc1.1.6.L93.90" scanType="progressive">
      </Representation>
      <Representation id="video=1495476" bandwidth="1495476" width="1920" height="1080" codecs="hvc1.1.6.L120.90" scanType="progressive">
      </Representation>
    </AdaptationSet>
  </Period>
</MPD>