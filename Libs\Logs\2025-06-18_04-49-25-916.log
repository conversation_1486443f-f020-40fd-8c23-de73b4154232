﻿LOG 2025/06/18
Save Path: C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Libs\Logs
Task Start: 2025/06/18 04:49:25
Task CommandLine: "C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Libs\n_m3u8dl_re" https://pop1e4.cdn.intigral-ott.net:443/bpk-token/1ah@y1tvoa2kpw0bamcctca5ohl3aa44mvdayzqdogca/Titles_HEVC/S0010563001001/S0010563001001.ism/manifest.mpd -mt --append-url-params -sa lang=tr:for=best1 -sv res=540:for=best --tmp-dir "C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Downloads\Meta Aşk.S01-E01.540p.JAWWYTV.WEB-DL.Matego" --save-dir "C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Downloads\Meta Aşk.S01-E01.540p.JAWWYTV.WEB-DL.Matego" --save-name "Meta Aşk.S01-E01.540p.JAWWYTV.WEB-DL.Matego" --key 7ba1b3c0e41b4cb6c0f3c3e0a1a87d00:84a18e4be3f4bb7d5e083f966b21f3d6 --thread-count 20 --log-level=INFO --check-segments-count=False "--decryption-binary-path=C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Libs\mp4decrypt.exe" --del-after-done=False --binary-merge

04:49:25.919 INFO : N_m3u8DL-RE (Beta version) 20230628
04:49:25.927 EXTRA: ffmpeg => C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Libs\ffmpeg.exe
04:49:25.927 INFO : Loading URL: https://pop1e4.cdn.intigral-ott.net:443/bpk-token/1ah@y1tvoa2kpw0bamcctca5ohl3aa44mvdayzqdogca/Titles_HEVC/S0010563001001/S0010563001001.ism/manifest.mpd
04:49:26.260 INFO : New version detected! v0.3.0-beta
04:49:26.320 INFO : Content Matched: Dynamic Adaptive Streaming over HTTP
04:49:26.321 INFO : Parsing streams...
04:49:26.335 WARN : Writing meta json
04:49:26.338 INFO : Extracted, there are 7 streams, with 5 basic streams, 1 audio streams, 1 subtitle streams
04:49:26.340 INFO : Vid *CENC 1920x1080 | 1495 Kbps | video=1495476 | hvc1.1.6.L120.90 | 361 Segments | ~12m00s
04:49:26.340 INFO : Vid *CENC 1280x720 | 641 Kbps | video=641481 | hvc1.1.6.L93.90 | 361 Segments | ~12m00s
04:49:26.341 INFO : Vid *CENC 960x540 | 433 Kbps | video=433026 | hvc1.1.6.L90.90 | 361 Segments | ~12m00s
04:49:26.341 INFO : Vid *CENC 768x432 | 334 Kbps | video=334453 | hvc1.1.6.L90.90 | 361 Segments | ~12m00s
04:49:26.342 INFO : Vid *CENC 512x288 | 197 Kbps | video=197851 | hvc1.1.6.L63.90 | 361 Segments | ~12m00s
04:49:26.342 INFO : Aud *CENC audio_tur=95995 | 95 Kbps | mp4a.40.2 | tr | 2CH | 361 Segments | ~12m01s
04:49:26.342 INFO : Sub textstream_ara=1000 | ar | stpp.ttml.im1t | 326 Segments | ~10m51s
04:49:26.343 EXTRA: VideoFilter => ResolutionReg: 540 For: best
04:49:26.343 EXTRA: AudioFilter => LanguageReg: tr For: best1
04:49:26.343 INFO : Parsing streams...
04:49:26.346 INFO : Selected streams:
04:49:26.346 INFO : Vid *CENC 960x540 | 433 Kbps | video=433026 | hvc1.1.6.L90.90 | 361 Segments | ~12m00s
04:49:26.347 INFO : Aud *CENC audio_tur=95995 | 95 Kbps | mp4a.40.2 | tr | 2CH | 361 Segments | ~12m01s
04:49:26.347 WARN : Writing meta json
04:49:26.349 INFO : Save Name: Meta Aşk.S01-E01.540p.JAWWYTV.WEB-DL.Matego
04:49:26.350 INFO : Start downloading...Vid 960x540 | 433 Kbps | video=433026 | hvc1.1.6.L90.90
04:49:26.350 INFO : Start downloading...Aud audio_tur=95995 | 95 Kbps | mp4a.40.2 | tr | 2CH
04:49:26.529 WARN : Type: cenc
04:49:26.530 WARN : PSSH(WV): CAESEHuhs8DkG0y2wPPD4KGofQAaGnZlcmltYXRyaXhndWxmZGlnaXRhbG1lZGlhIhdyPVMwMDEwNTYzMDAxMDAxJnM9MTAyNyoFU0RfSEQ=
04:49:26.533 WARN : KID: 7ba1b3c0e41b4cb6c0f3c3e0a1a87d00
04:49:26.536 WARN : Reading media info...
04:49:26.579 INFO : NaN: Video, hevc (Main) (hvc1), 960x540
04:49:26.661 WARN : Type: cenc
04:49:26.662 WARN : PSSH(WV): CAESEHuhs8DkG0y2wPPD4KGofQAaGnZlcmltYXRyaXhndWxmZGlnaXRhbG1lZGlhIhdyPVMwMDEwNTYzMDAxMDAxJnM9MTAyNyoFU0RfSEQ=
04:49:26.665 WARN : KID: 7ba1b3c0e41b4cb6c0f3c3e0a1a87d00
04:49:26.665 WARN : Reading media info...
04:49:26.702 INFO : NaN: Audio, aac (mp4a), 95 kb/s
04:49:36.171 INFO : Binary merging...
04:49:36.220 INFO : Decrypting...
04:49:42.349 INFO : Binary merging...
04:49:42.405 INFO : Decrypting...
04:49:42.424 INFO : Done
