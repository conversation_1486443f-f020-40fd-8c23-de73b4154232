import os, re, time, requests, html, http
import subprocess, ffmpy, json, uuid
import pycaption, pathlib, math

from shutil import which, copyfileobj, rmtree
from os.path import join, isfile, realpath, dirname, exists
from os import pathsep, environ, remove, makedirs
from unidecode import unidecode
from pymediainfo import MediaInfo
from random import randint
from urllib.parse import unquote

from m3u8 import parse as m3u8parser
from urllib.parse import urljoin

config = {
    'proxies': {
        'none': None
    },
}

class ProxyConfig(object):
    def __init__(self, proxies):
        self.config = config
        self.config['proxies'] = proxies

    def get_proxy(self, proxy):
        return self.config['proxies'].get(proxy)

def parseCookieFile(cookies_file):
    cookies = {}
    with open(cookies_file, 'r') as fp:
        for line in fp:
            if not re.match(r'^\#', line):
                lineFields = line.strip().split('\t')
                cookies[lineFields[5]] = lineFields[6]
    return cookies

def generate_device_id():
    return str(uuid.uuid4())

def get_episodes(ep_str, num_eps):
    eps = ep_str.split(',')
    eps_final = []

    for ep in eps:
        if '-' in ep:
            (start, end) = ep.split('-')
            start = int(start)
            end = int(end or num_eps)
            eps_final += list(range(start, end + 1))
        else:
            eps_final.append(int(ep))

    return eps_final

def replace_words(x):
    x = x.replace('\\', '').replace('/', ' & ').replace('«', '').replace('»', '').replace(' - ', ' ').replace('"', '').replace('é', 'e')
    x = re.sub(r'[]¡!?¿“”"#$%\'()*+,:;<=>¿?@\\^_;`{|}~[-]', '', x)
    return x

def global_replace_code_lang(X):
    X = X.lower()
    X = X.replace('_subtitle_dialog_0', '').replace('_narrative_dialog_0', '').replace('_caption_dialog_0', '').replace('_dialog_0', '').replace('_descriptive_0', '-ad').replace('_descriptive', '_descriptive').replace('_sdh', '-sdh').replace('es-es', 'es').replace('en-es', 'es').replace('kn-in', 'kn').replace('gu-in', 'gu').replace('ja-jp', 'ja').replace('mni-in', 'mni').replace('si-in', 'si').replace('as-in', 'as').replace('ml-in', 'ml').replace('sv-se', 'sv').replace('hy-hy', 'hy').replace('sv-sv', 'sv').replace('da-da', 'da').replace('fi-fi', 'fi').replace('nb-nb', 'nb').replace('is-is', 'is').replace('uk-uk', 'uk').replace('hu-hu', 'hu').replace('bg-bg', 'bg').replace('hr-hr', 'hr').replace('lt-lt', 'lt').replace('et-et', 'et').replace('el-el', 'el').replace('he-he', 'he').replace('ar-ar', 'ar').replace('fa-fa', 'fa').replace('ro-ro', 'ro').replace('sr-sr', 'sr').replace('cs-cs', 'cs').replace('sk-sk', 'sk').replace('mk-mk', 'mk').replace('hi-hi', 'hi').replace('bn-bn', 'bn').replace('ur-ur', 'ur').replace('pa-pa', 'pa').replace('ta-ta', 'ta').replace('te-te', 'te').replace('mr-mr', 'mr').replace('kn-kn', 'kn').replace('gu-gu', 'gu').replace('ml-ml', 'ml').replace('si-si', 'si').replace('as-as', 'as').replace('mni-mni', 'mni').replace('tl-tl', 'tl').replace('id-id', 'id').replace('ms-ms', 'ms').replace('vi-vi', 'vi').replace('th-th', 'th').replace('km-km', 'km').replace('ko-ko', 'ko').replace('zh-zh', 'zh').replace('ja-ja', 'ja').replace('ru-ru', 'ru').replace('tr-tr', 'tr').replace('it-it', 'it').replace('es-mx', 'es-la').replace('ar-sa', 'ar').replace('zh-cn', 'zh').replace('nl-nl', 'nl').replace('pl-pl', 'pl').replace('pt-pt', 'pt').replace('hi-in', 'hi').replace('mr-in', 'mr').replace('bn-in', 'bn').replace('te-in', 'te').replace('cmn-hans', 'zh-hans').replace('cmn-hant', 'zh-hant').replace('ko-kr', 'ko').replace('en-au', 'en').replace('es-419', 'es-la').replace('es-us', 'es-la').replace('en-us', 'en').replace('en-gb', 'en').replace('fr-fr', 'fr').replace('de-de', 'de').replace('las-419', 'es-la').replace('ar-ae', 'ar').replace('da-dk', 'da').replace('yue-hant', 'yue').replace('bn-in', 'bn').replace('ur-in', 'ur').replace('ta-in', 'ta').replace('sl-si', 'sl').replace('cs-cz', 'cs').replace('hi-jp', 'hi').replace('-001', '').replace('en-US', 'en').replace('deu', 'de').replace('eng', 'en').replace('ca-es', 'cat').replace('fil-ph', 'fil').replace('en-ca', 'en').replace('eu-es', 'eu').replace('ar-eg', 'ar').replace('he-il', 'he').replace('el-gr', 'he').replace('nb-no', 'nb').replace('es-ar', 'es-la').replace('en-ph', 'en').replace('sq-al', 'sq').replace('bs-ba', 'bs').replace('pt-BR', 'pt-br')
    return X

def alphanumericSort(l):
    def convert(text):
        if text.isdigit():
            return int(text)
        else:
            return text

    def alphanum_key(key):
        return [convert(c) for c in re.split('([0-9]+)', key)]

    return sorted(l, key=alphanum_key)

def pretty_size(size):
    if size < 1073741824:
        size = f'{size / 1048576:0.2f} MiB'
    else:
        size = f'{size / 1073741824:0.2f} GiB'
    return size

def convert_size(size_bytes):
    if size_bytes == 0:
        return '0 bps'
    else:
        s = round(size_bytes / 1000, 0)
        return '%i kbps' % s

def get_size(size):
    power = 1024
    n = 0
    Dic_powerN = {0:' ',  1:' K',  2:' M',  3:' G',  4:' T'}
    while size > power:
        size /= power
        n += 1
    return str(round(size, 2)) + Dic_powerN[n] + 'iB'

def get_subtitle_download(url):
    try:
        req = requests.get(url, timeout=15)
    except Exception:
        while True:
            try:
                req = requests.get(url, timeout=15)
                return req
            except Exception:
                continue
    return req

def find_str(s, char):
    index = 0

    if char in s:
        c = char[0]
        for ch in s:
            if ch == c:
                if s[index:index+len(char)] == char:
                    return index

            index += 1

    return -1

def getKeyId(name):
    mp4dump = subprocess.Popen([MP4DUMP, name], stdout=subprocess.PIPE)
    mp4dump = str(mp4dump.stdout.read())
    A=find_str(mp4dump, "default_KID")
    KEY_ID_ORI=mp4dump[A:A+63].replace("default_KID = ", "").replace("[", "").replace("]", "").replace(" ", "")
    if KEY_ID_ORI == "":
        KEY_ID_ORI = "nothing"
    return KEY_ID_ORI

def mediainfo_(file):
    mediainfo_output = subprocess.Popen([MEDIAINFO, '--Output=JSON', '-f', file], stdout=subprocess.PIPE)
    mediainfo_json = json.load(mediainfo_output.stdout)
    return mediainfo_json

def release_group(base_filename, default_filename, folder_name, streaming, type, tag):
    if type=='show':
        video_mkv = os.path.join(folder_name, base_filename)
    else:
        video_mkv = base_filename
    
    audio_codecs_dict = {
        'E-AC-3': 'DDP',
        'AC-3': 'DD',
        'DTS': 'DTS',
        'AAC': 'AAC',
        'QUICKTIME': 'QUICKTIME'  # Added handling for QUICKTIME
    }
    channels_dict = {
        '8': '7.1',
        '6': '5.1',
        '2': '2.0'
    }
    video_dict = {
        'writing_library': {
            'AVC': 'x264',
            'HEVC': 'x265',
            'AV1': 'AV1',
            'QUICKTIME': 'QUICKTIME'  # Added handling for QUICKTIME
        },
        'library': {
            'AVC': 'H.264',
            'HEVC': 'HEVC',
            'AV1': 'AV1',
            'QUICKTIME': 'QUICKTIME'  # Added handling for QUICKTIME
        }
    }
    hdr_formats_dict = {
        'BT.2020': 'HDR',
        'PQ': 'HDR',
        'Dolby Vision': 'DV'
    }

    media_info = MediaInfo.parse(video_mkv)
    for track in media_info.tracks:
        if track.track_type == 'Video':
            if track.encoding_settings and track.writing_library:
                format = video_dict['writing_library'].get(track.format, 'UNKNOWN')
            else:
                format = video_dict['library'].get(track.format, 'UNKNOWN')

            if track.color_primaries and track.color_primaries in hdr_formats_dict:
                hdr_format = hdr_formats_dict[track.color_primaries.upper()]
            elif track.transfer_characteristics and track.transfer_characteristics in hdr_formats_dict:
                hdr_format = hdr_formats_dict[track.transfer_characteristics.upper()]
            elif track.hdr_format and track.hdr_format in hdr_formats_dict:
                hdr_format = hdr_formats_dict[track.hdr_format]
            else:
                hdr_format = False

            if hdr_format:
                video = '{}.{}'.format(hdr_format, format)
            else:
                video = '{}'.format(format)

            if int(track.height) == 2160 or int(track.width) >= 2500:
                quality = '2160p'
            elif int(track.height) == 1080 or int(track.width) >= 1500:
                quality = '1080p'
            elif int(track.height) == 720 or int(track.width) >= 1200:
                quality = '720p'
            else:
                quality = f'{int(track.height)}p'

        elif track.track_type == 'Audio':
            if int(track.count_of_stream_of_this_kind) == 2:
                dual = 'DUAL'
                audios = {
                    'total': int(track.count_of_stream_of_this_kind),
                    'mood': 'DUAL.DD+5.1'
                }
            elif int(track.count_of_stream_of_this_kind) > 2:
                dual = 'MULTi.DD+5.1'
                audios = {
                    'total': int(track.count_of_stream_of_this_kind),
                    'mood': 'MULTi.DD+5.1'
                }
            else:
                bitrate = track.bit_rate
                channels = track.channel_s
                format = track.format
                audios = {
                    'total': int(track.count_of_stream_of_this_kind),
                    'bitrate': int(bitrate) if bitrate else 0,
                    'codec': audio_codecs_dict.get(format, 'UNKNOWN'),
                    'channels': channels_dict.get(str(channels), 'UNKNOWN'),
                    'channel_s': int(channels)
                }

    default_filename = default_filename.replace('&', '.and.')
    default_filename = default_filename.replace('-', '.')
    default_filename = re.sub(r'[]!"#$%\'()*+,:;<=>?@\\^_`{|}~[]', '', default_filename)
    default_filename = default_filename.replace(' ', '.')
    default_filename = re.sub(r'\.{2,}', '.', default_filename)

    if audios['total'] == 2:
        output_name = '{}.{}.{}.WEB-DL.{}.{}-{}'.format(default_filename, quality, streaming, audios['mood'], video, tag)
    elif audios['total'] > 2:
        output_name = '{}.{}.{}.WEB-DL.{}.{}-{}'.format(default_filename, quality, streaming, audios['mood'], video, tag)
    else:
        output_name = '{}.{}.{}.WEB-DL.{}{}.{}-{}'.format(default_filename, quality, streaming, audios['codec'], audios['channels'], video, tag)

    if type=='show':
        tag_video_mkv = os.path.join(folder_name, output_name + '.mkv')
    else:
        tag_video_mkv = output_name + '.mkv'
    os.rename(video_mkv, tag_video_mkv)
    print("{} -> {}".format(base_filename, output_name))

def DecryptAudio(inputAudio, keys_audio):
    key_audio_id_original = getKeyId(inputAudio)
    outputAudioTemp = inputAudio.replace('.mp4', '_dec.mp4')
    if key_audio_id_original != 'nothing':
        for key in keys_audio:
            key_id = key[0:32]
            if key_id == key_audio_id_original:
                print('\nDecrypting audio...')
                print('Using KEY: ' + key)
                wvdecrypt_process = subprocess.Popen([MP4DECRYPT, '--show-progress', '--key', key, inputAudio, outputAudioTemp])
                stdoutdata, stderrdata = wvdecrypt_process.communicate()
                wvdecrypt_process.wait()
                time.sleep(0.05)
                os.remove(inputAudio)
                print('\nDemuxing audio...')
                mediainfo = mediainfo_(outputAudioTemp)
                for m in mediainfo['media']['track']:
                    if m['@type'] == 'Audio':
                        codec_name = m['Format']
                ext = ''
                if codec_name == "AAC":
                    ext = '.m4a'
                elif codec_name == "E-AC-3":
                    ext = ".eac3"
                elif codec_name == "AC-3":
                    ext = ".ac3"
                outputAudio = outputAudioTemp.replace("_dec.mp4", ext)
                print("{} -> {}".format(outputAudioTemp, outputAudio))
                ff = ffmpy.FFmpeg(executable=FFMPEG, inputs={outputAudioTemp: None}, outputs={outputAudio: '-c copy'}, global_options="-y -hide_banner -loglevel warning")
                ff.run()
                time.sleep (50.0/1000.0)
                os.remove(outputAudioTemp)
                print("Done!")
                return outputAudio  # Return the path to the decrypted audio file

    elif key_audio_id_original == "nothing":
        return inputAudio  # Return the original file if nothing to decrypt

def DecryptAudio_alt(inputAudio, keys_audio, ffmpeg=False):
    key_video_id_original = getKeyId(inputAudio)
    outputAudioTemp = inputAudio.replace('.mp4', '_dec.mp4')
    if key_video_id_original != 'nothing':
        print('\nDecrypting audio...')
        print('Using KEY: ' + keys_audio[0])
        keys_arg = ",".join([f"label=HD{randint(0,1000)}:key_id={kid}:key={key}" for kid, key in map(lambda x: x.split(":"), keys_audio)])
        subprocess.run([
            SHAKA_PACKAGER,
            "input={},stream={},output={}".format(
                inputAudio,
                "audio",
                outputAudioTemp
            ),
            "-quiet", "--enable_raw_key_decryption", "--keys",
            keys_arg
        ], check=True)

        mediainfo = mediainfo_(outputAudioTemp)
        for m in mediainfo['media']['track']:
            if m['@type'] == 'Audio':
                codec_name = m['Format']
        ext = ''
        if codec_name == "AAC":
            ext = '.m4a'
        elif codec_name == "E-AC-3":
            ext = ".eac3"
        elif codec_name == "AC-3":
            ext = ".ac3"
        outputAudio = outputAudioTemp.replace("_dec.mp4", ext)

        if ffmpeg:
            print('\nDemuxing audio...')
            ff = ffmpy.FFmpeg(executable=FFMPEG, inputs={outputAudioTemp: None}, outputs={outputAudio: '-c copy'}, global_options="-y -hide_banner -loglevel warning")
            ff.run()
            time.sleep(50.0/1000.0)
            os.remove(outputAudioTemp)
        else:
            os.rename(outputAudioTemp, outputAudio)
        os.remove(inputAudio)
        print("{} -> {}".format(outputAudioTemp, outputAudio))
        print('Done!')
        return outputAudio  # Return the path to the decrypted audio file

    elif key_video_id_original == 'nothing':
        return inputAudio  # Return the original file if nothing to decrypt

def DemuxAudio(inputAudio):
    outputAudio_mka = inputAudio.replace('.mp4', '.mka')
    print('\nDemuxing audio...')
    mkvmerge_command = [MKVMERGE, '-q', '--output', outputAudio_mka, '--language', '0:und', inputAudio]
    mkvmerge_process = subprocess.run(mkvmerge_command)
    print("{} -> {}".format(inputAudio, outputAudio_mka))
    time.sleep (50.0/1000.0)
    os.remove(inputAudio)
    print("Done!")
    return outputAudio_mka  # Return the path to the demuxed audio file

def DecryptVideo(inputVideo, keys_video):
    key_video_id_original = getKeyId(inputVideo)
    outputVideoTemp = inputVideo.replace('.mp4', '_dec.mp4')
    if key_video_id_original != 'nothing':
        for key in keys_video:
            key_id = key[0:32]
            if key_id == key_video_id_original:
                print('\nDecrypting video...')
                print('Using KEY: ' + key)
                wvdecrypt_process = subprocess.Popen([MP4DECRYPT, '--show-progress', '--key', key, inputVideo, outputVideoTemp])
                stdoutdata, stderrdata = wvdecrypt_process.communicate()
                wvdecrypt_process.wait()
                print('\nRemuxing video...')
                ff = ffmpy.FFmpeg(executable=FFMPEG, inputs={outputVideoTemp: None}, outputs={inputVideo: '-c copy'}, global_options='-y -hide_banner -loglevel warning')
                ff.run()
                time.sleep(0.05)
                os.remove(outputVideoTemp)
                print('Done!')
                return inputVideo  # Return the path to the decrypted video file

    elif key_video_id_original == 'nothing':
        return inputVideo  # Return the original file if nothing to decrypt

def DecryptVideo_alt(inputVideo, keys_video, ffmpeg=False):
    key_video_id_original = getKeyId(inputVideo)
    outputVideoTemp = inputVideo.replace('.mp4', '_dec.mp4')
    
    if key_video_id_original != 'nothing':
        print('\nDecrypting video...')
        print('Using KEY: ' + keys_video[0])
        keys_arg = ",".join([f"label=UHD{randint(0,1000)}:key_id={kid}:key={key}" for kid, key in map(lambda x: x.split(":"), keys_video)])
        subprocess.run([
            SHAKA_PACKAGER,
            "input={},stream={},output={}".format(
                inputVideo,
                "video",
                outputVideoTemp
            ),
            "-quiet", "--enable_raw_key_decryption", "--keys",
            keys_arg
        ], check=True)

        if ffmpeg:
            print('\nRemuxing video...')
            ff = ffmpy.FFmpeg(executable=FFMPEG, inputs={outputVideoTemp: None}, outputs={inputVideo: '-c copy'}, global_options='-y -hide_banner -loglevel warning')
            ff.run()
            time.sleep(0.05)
            os.remove(outputVideoTemp)
        else:
            os.remove(inputVideo)
            os.rename(outputVideoTemp, inputVideo)
        print('Done!')
        return inputVideo  # Return the path to the decrypted video file

    elif key_video_id_original == 'nothing':
        return inputVideo  # Return the original file if nothing to decrypt

def RemuxVideo(inputVideo):
    outputVideoTemp = inputVideo.replace(".mp4", "_dec.mp4")
    os.rename(inputVideo, outputVideoTemp)
    print("\nRemuxing video...")
    ff = ffmpy.FFmpeg(executable=FFMPEG, inputs={outputVideoTemp: None}, outputs={inputVideo: '-c copy'}, global_options="-y -hide_banner -loglevel warning")
    ff.run()
    time.sleep(0.05)
    os.remove(outputVideoTemp)
    print("Done!")
    return inputVideo  # Return the path to the remuxed video file

def leading_zeros(value, digits=2):
    value = "000000" + str(value)
    return value[-digits:]

def convert_time(raw_time):
    if int(raw_time) == 0:
        return "{}:{}:{},{}".format(0, 0, 0, 0)

    ms = '000'
    if len(raw_time) > 4:
        ms = leading_zeros(int(raw_time[:-4]) % 1000, 3)
    time_in_seconds = int(raw_time[:-7]) if len(raw_time) > 7 else 0
    second = leading_zeros(time_in_seconds % 60)
    minute = leading_zeros(int(math.floor(time_in_seconds / 60)) % 60)
    hour = leading_zeros(int(math.floor(time_in_seconds / 3600)))
    return "{}:{}:{},{}".format(hour, minute, second, ms)

def to_srt(text):
    def append_subs(start, end, prev_content, format_time):
        subs.append({
            "start_time": convert_time(start) if format_time else start,
            "end_time": convert_time(end) if format_time else end,
            "content": u"\n".join(prev_content),
        })

    begin_re = re.compile(u"\s*<p begin=")
    sub_lines = (l for l in text.split("\n") if re.search(begin_re, l))
    subs = []
    prev_time = {"start": 0, "end": 0}
    prev_content = []
    start = end = ''
    start_re = re.compile(u'begin\="([0-9:\.]*)')
    end_re = re.compile(u'end\="([0-9:\.]*)')
    # this regex was sometimes too strict. I hope the new one is never too lax
    # content_re = re.compile(u'xml\:id\=\"subtitle[0-9]+\">(.*)</p>')
    content_re = re.compile(u'\">(.*)</p>')
    #alt_content_re = re.compile(u'<span style=\"[a-zA-Z0-9_]+\">(.*?)</span>')
    alt_content_re = re.compile(u'<span style=\"[a-zA-Z0-9_\.]+\">(.*?)</span>')
    br_re = re.compile(u'(<br\s*\/?>)+')
    fmt_t = True
    for s in sub_lines:
        content = []
        alt_content = re.search(alt_content_re, s)
        while (alt_content):  # background text may have additional styling.
            # background may also contain several `<span> </span>` groups
            s = s.replace(alt_content.group(0), alt_content.group(1))
            alt_content = re.search(alt_content_re, s)
        content = re.search(content_re, s).group(1)

        br_tags = re.search(br_re, content)
        if br_tags:
            content = u"\n".join(content.split(br_tags.group()))

        prev_start = prev_time["start"]
        start = re.search(start_re, s).group(1)
        end = re.search(end_re, s).group(1)
        if len(start.split(":")) > 1:
            fmt_t = False
            start = start.replace(".", ",")
            end = end.replace(".", ",")
        if (prev_start == start and prev_time["end"] == end) or not prev_start:
            # Fix for multiple lines starting at the same time
            prev_time = {"start": start, "end": end}
            prev_content.append(content)
            continue
        append_subs(prev_time["start"], prev_time["end"], prev_content, fmt_t)
        prev_time = {"start": start, "end": end}
        prev_content = [content]
    append_subs(start, end, prev_content, fmt_t)

    lines = (u"{}\n{} --> {}\n{}\n".format(
        s + 1, subs[s]["start_time"], subs[s]["end_time"], subs[s]["content"])
        for s in range(len(subs)))
    return u"\n".join(lines)

def vtt_to_srt(text):
    header = re.compile(r'WEBVTT.*?(?=^\d+$\s+^\d+:)', re.DOTALL | re.MULTILINE)
    timecode = re.compile(
        r'^^(\d{2}:\d{2}:\d{2})(.)(\d{3}) --> (\d{2}:\d{2}:\d{2})(.)(\d{3}).*',
        re.MULTILINE
    )
    whitespace = re.compile(r'^\s*$', re.MULTILINE)

    srt = re.sub(header, '', text)
    srt = re.sub(timecode, r'\1,\3 --> \4,\6', srt)
    srt = re.sub(r'</?[cv][^>]+>', '', srt)
    srt = re.sub(whitespace, '', srt)
    srt = html.unescape(srt)
    return srt

def finagle_subs(subtitles, type):
    if type == 'vtt':
        return vtt_to_srt(subtitles)
    else:
        return to_srt(subtitles)

SCRIPT_PATH = dirname(realpath('bad37'))

BINARIES_FOLDER = join(SCRIPT_PATH, 'binaries')
KEYS_FOLDER = join(SCRIPT_PATH, 'KEYS')
COOKIES_FOLDER = join(SCRIPT_PATH, 'cookies')

MP4DECRYPT_BINARY = 'mp4decrypt'
CCEXTRACTOR_BINARY = 'ccextractor'
SHAKA_PACKAGER_BINARY = 'packager'
MEDIAINFO_BINARY = 'mediainfo'
MP4DUMP_BINARY = 'mp4dump'
MKVMERGE_BINARY = 'mkvmerge'
FFMPEG_BINARY = 'ffmpeg'
ARIA2C_BINARY = 'aria2c'
M3U8_DL_RE_BINARY = 'm3u8dl_re'
SUBTITLE_EDIT_BINARY = 'SubtitleEdit'

# Add binaries folder to PATH as the first item
environ['PATH'] = pathsep.join([BINARIES_FOLDER, environ['PATH']])

SHAKA_PACKAGER = which(SHAKA_PACKAGER_BINARY)
MP4DECRYPT = which(MP4DECRYPT_BINARY)
CCEXTRACTOR = which(CCEXTRACTOR_BINARY)
MEDIAINFO = which(MEDIAINFO_BINARY)
MP4DUMP = which(MP4DUMP_BINARY)
MKVMERGE = which(MKVMERGE_BINARY)
FFMPEG = which(FFMPEG_BINARY)
ARIA2C = which(ARIA2C_BINARY)
M3U8_DL_RE = which(M3U8_DL_RE_BINARY)
SUBTITLE_EDIT = which(SUBTITLE_EDIT_BINARY)