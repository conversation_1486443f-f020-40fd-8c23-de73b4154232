﻿<?xml version="1.0" encoding="utf-8"?>
<!-- Created with Broadpeak BkS350 Origin Packager (version=1.11.16-26430) -->
<MPD xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="urn:mpeg:dash:schema:mpd:2011" xmlns:cenc="urn:mpeg:cenc:2013" xsi:schemaLocation="urn:mpeg:dash:schema:mpd:2011 http://standards.iso.org/ittf/PubliclyAvailableStandards/MPEG-DASH_schema_files/DASH-MPD.xsd" type="static" mediaPresentationDuration="PT14M13.120S" maxSegmentDuration="PT3S" minBufferTime="PT12S" profiles="urn:mpeg:dash:profile:isoff-live:2011">
  <Period id="1" duration="PT14M13.120S">
    <BaseURL>dash/</BaseURL>
    <AdaptationSet id="1" group="1" contentType="audio" lang="tr" segmentAlignment="true" audioSamplingRate="48000" mimeType="audio/mp4" codecs="mp4a.40.2" startWithSAP="1">
      <AudioChannelConfiguration schemeIdUri="urn:mpeg:dash:23003:3:audio_channel_configuration:2011" value="2"/>
      <!-- Common Encryption -->
      <ContentProtection schemeIdUri="urn:mpeg:dash:mp4protection:2011" value="cenc" cenc:default_KID="0CB93255-A33D-AE85-AEE1-981190C0E75C">
      </ContentProtection>
      <!-- PlayReady -->
      <ContentProtection schemeIdUri="urn:uuid:9A04F079-9840-4286-AB92-E65BE0885F95" value="MSPR 2.0">
      </ContentProtection>
      <!-- Widevine -->
      <ContentProtection schemeIdUri="urn:uuid:EDEF8BA9-79D6-4ACE-A3C8-27DCD51D21ED">
      </ContentProtection>
      <Role schemeIdUri="urn:mpeg:dash:role:2011" value="main"/>
      <SegmentTemplate timescale="48000" initialization="S0010563001002-$RepresentationID$.dash" media="S0010563001002-$RepresentationID$-$Time$.dash">
        <SegmentTimeline>
          <S t="0" d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256" r="2"/>
          <S d="95232"/>
          <S d="96256"/>
          <S d="96000"/>
          <S d="59392"/>
        </SegmentTimeline>
      </SegmentTemplate>
      <Representation id="audio_tur=95997" bandwidth="95997">
      </Representation>
    </AdaptationSet>
    <AdaptationSet id="2" group="3" contentType="text" lang="ar" mimeType="application/mp4" codecs="stpp.ttml.im1t" startWithSAP="1">
      <Role schemeIdUri="urn:mpeg:dash:role:2011" value="subtitle"/>
      <SegmentTemplate timescale="1000" initialization="S0010563001002-$RepresentationID$.dash" media="S0010563001002-$RepresentationID$-$Time$.dash">
        <SegmentTimeline>
          <S t="0" d="2000" r="407"/>
          <S d="1240"/>
        </SegmentTimeline>
      </SegmentTemplate>
      <Representation id="textstream_ara=1000" bandwidth="1000">
      </Representation>
    </AdaptationSet>
    <AdaptationSet id="3" group="2" contentType="video" par="16:9" minBandwidth="137664" maxBandwidth="861805" maxWidth="1280" maxHeight="720" segmentAlignment="true" sar="1:1" mimeType="video/mp4" startWithSAP="1">
      <!-- Common Encryption -->
      <ContentProtection schemeIdUri="urn:mpeg:dash:mp4protection:2011" value="cenc" cenc:default_KID="0CB93255-A33D-AE85-AEE1-981190C0E75C">
      </ContentProtection>
      <!-- PlayReady -->
      <ContentProtection schemeIdUri="urn:uuid:9A04F079-9840-4286-AB92-E65BE0885F95" value="MSPR 2.0">
      </ContentProtection>
      <!-- Widevine -->
      <ContentProtection schemeIdUri="urn:uuid:EDEF8BA9-79D6-4ACE-A3C8-27DCD51D21ED">
      </ContentProtection>
      <Role schemeIdUri="urn:mpeg:dash:role:2011" value="main"/>
      <SegmentTemplate timescale="12800" initialization="S0010563001002-$RepresentationID$.dash" media="S0010563001002-$RepresentationID$-$Time$.dash">
        <SegmentTimeline>
          <S t="0" d="25600" r="425"/>
          <S d="14336"/>
        </SegmentTimeline>
      </SegmentTemplate>
      <Representation id="video=137664" bandwidth="137664" width="384" height="216" codecs="avc1.640014" scanType="progressive">
      </Representation>
      <Representation id="video=201250" bandwidth="201250" width="512" height="288" codecs="avc1.640015" scanType="progressive">
      </Representation>
      <Representation id="video=369019" bandwidth="369019" width="768" height="432" codecs="avc1.64001E" scanType="progressive">
      </Representation>
      <Representation id="video=516675" bandwidth="516675" width="960" height="540" codecs="avc1.64001F" scanType="progressive">
      </Representation>
      <Representation id="video=861805" bandwidth="861805" width="1280" height="720" codecs="avc1.64001F" scanType="progressive">
      </Representation>
    </AdaptationSet>
  </Period>
</MPD>