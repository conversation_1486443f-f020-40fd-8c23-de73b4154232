﻿LOG 2025/06/18
Save Path: C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Libs\Logs
Task Start: 2025/06/18 04:40:33
Task CommandLine: "C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Libs\n_m3u8dl_re" https://pop1e3.cdn.intigral-ott.net:443/bpk-token/1ai@gfwh2u3vy4qtgxtdsl0dtmpyjtjr3bhng5rr2sda/Titles/S0010563001002/S0010563001002.ism/manifest.mpd -mt --append-url-params -sa lang=tr:for=best1 -sv res=288:for=best --tmp-dir "C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Downloads\Meta Aşk.S01-E02.288p.JAWWYTV.WEB-DL.Matego" --save-dir "C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Downloads\Meta Aşk.S01-E02.288p.JAWWYTV.WEB-DL.Matego" --save-name "Meta Aşk.S01-E02.288p.JAWWYTV.WEB-DL.Matego" --key 0cb93255a33dae85aee1981190c0e75c:99a8b13ee1245ea6f70f0a1c2e7304b7 --thread-count 20 --log-level=INFO --check-segments-count=False "--decryption-binary-path=C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Libs\mp4decrypt.exe" --del-after-done=False --binary-merge --mux-import "path=C\:\Users\musta\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Downloads\Meta Aşk.S01-E02.288p.JAWWYTV.WEB-DL.Matego\Meta Aşk.S01-E02.288p.JAWWYTV.WEB-DL.Matego.ar.srt:lang=ara:name=Arabic"

04:40:33.264 INFO : N_m3u8DL-RE (Beta version) 20230628
04:40:33.272 ERROR: MuxAfterDone disabled, MuxImports not allowed!
04:40:33.534 INFO : New version detected! v0.3.0-beta
