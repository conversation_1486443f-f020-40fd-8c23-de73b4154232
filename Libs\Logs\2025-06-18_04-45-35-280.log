﻿LOG 2025/06/18
Save Path: C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Libs\Logs
Task Start: 2025/06/18 04:45:35
Task CommandLine: "C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Libs\n_m3u8dl_re" https://pop1e1.cdn.intigral-ott.net:443/bpk-token/1ak@cn51lupq3pprogvula0kd4xxixi154c3cxpkhsba/Titles/S0010563001001/S0010563001001.ism/manifest.mpd -mt --append-url-params -sa lang=tr:for=best1 -sv res=432:for=best --tmp-dir "C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Downloads\Meta Aşk.S01-E01.432p.JAWWYTV.WEB-DL.Matego" --save-dir "C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Downloads\Meta Aşk.S01-E01.432p.JAWWYTV.WEB-DL.Matego" --save-name "Meta Aşk.S01-E01.432p.JAWWYTV.WEB-DL.Matego" --key 7ba1b3c0e41b4cb6c0f3c3e0a1a87d00:84a18e4be3f4bb7d5e083f966b21f3d6 --thread-count 20 --log-level=INFO --check-segments-count=False "--decryption-binary-path=C:\Users\<USER>\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Libs\mp4decrypt.exe" --del-after-done=False --binary-merge --mux-import "path=C\:\Users\musta\Downloads\Telegram Desktop\JawwyTV+StcTV\JawwyTV+Stc\Downloads\Meta Aşk.S01-E01.432p.JAWWYTV.WEB-DL.Matego\Meta Aşk.S01-E01.432p.JAWWYTV.WEB-DL.Matego.ar.srt:lang=ara:name=Arabic"

04:45:35.282 INFO : N_m3u8DL-RE (Beta version) 20230628
04:45:35.294 ERROR: MuxAfterDone disabled, MuxImports not allowed!
04:45:35.549 INFO : New version detected! v0.3.0-beta
